#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的EyeLink校准验证程序
功能：
1. 执行EyeLink标准校准
2. 在屏幕上随机显示光点
3. 实时显示眼动位置
4. 验证校准精度

基于EyeLink官方示例简化而来
"""

import pylink
import random
import time
import sys
import os
from psychopy import visual, core, event, monitors
from psychopy.hardware import keyboard
import numpy as np

# 添加EyeLink图形库路径
script_path = os.path.dirname(os.path.abspath(__file__))
eyelink_example_path = os.path.join(script_path, "eyelink python example", "examples", "Psychopy_examples", "Coder", "saccade")
sys.path.append(eyelink_example_path)

try:
    from EyeLinkCoreGraphicsPsychoPy import EyeLinkCoreGraphicsPsychoPy
except ImportError:
    print("警告：无法导入EyeLinkCoreGraphicsPsychoPy，将使用虚拟模式")
    EyeLinkCoreGraphicsPsychoPy = None

class SimpleCalibrationValidator:
    """简化的校准验证系统"""
    
    def __init__(self, dummy_mode=False, fullscreen=True):
        """
        初始化校准验证系统
        
        Args:
            dummy_mode: 是否使用虚拟模式（无EyeLink连接）
            fullscreen: 是否全屏显示
        """
        self.dummy_mode = dummy_mode
        self.fullscreen = fullscreen
        self.tracker = None
        self.win = None
        self.genv = None
        self.is_connected = False
        
        # 验证点参数
        self.validation_points = []
        self.current_point_index = 0
        self.gaze_data = []
        
        print("🎯 简化EyeLink校准验证程序")
        print("=" * 50)
    
    def initialize_display(self):
        """初始化显示窗口"""
        try:
            # 创建显示器配置
            mon = monitors.Monitor('testMonitor', width=53.0, distance=70.0)
            
            # 创建窗口
            self.win = visual.Window(
                fullscr=self.fullscreen,
                monitor=mon,
                winType='pyglet',
                units='pix',
                color=(0.5, 0.5, 0.5)  # 灰色背景
            )
            
            # 获取屏幕尺寸
            self.screen_width, self.screen_height = self.win.size
            print(f"✓ 显示窗口创建成功: {self.screen_width}x{self.screen_height}")
            
            return True
            
        except Exception as e:
            print(f"✗ 显示窗口创建失败: {e}")
            return False
    
    def connect_eyelink(self):
        """连接EyeLink眼动仪"""
        if self.dummy_mode:
            print("🔄 虚拟模式：跳过EyeLink连接")
            return True
        
        try:
            # 连接EyeLink
            self.tracker = pylink.EyeLink("100.1.1.1")
            
            # 配置屏幕参数
            screen_coords = f"screen_pixel_coords = 0 0 {self.screen_width-1} {self.screen_height-1}"
            self.tracker.sendCommand(screen_coords)
            
            # 设置校准类型（9点校准）
            self.tracker.sendCommand("calibration_type = HV9")
            
            # 设置采样率
            self.tracker.sendCommand("sample_rate 1000")
            
            # 设置数据记录类型
            self.tracker.sendCommand("file_event_filter = LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE")
            self.tracker.sendCommand("file_sample_data = LEFT,RIGHT,GAZE,AREA,GAZERES,STATUS")
            self.tracker.sendCommand("link_sample_data = LEFT,RIGHT,GAZE,GAZERES,AREA,STATUS")
            
            # 打开数据文件
            self.tracker.openDataFile("calib_test.edf")
            
            self.is_connected = True
            print("✓ EyeLink连接成功")
            return True
            
        except Exception as e:
            print(f"✗ EyeLink连接失败: {e}")
            print("🔄 切换到虚拟模式")
            self.dummy_mode = True
            return True
    
    def setup_calibration_graphics(self):
        """设置校准图形环境"""
        if self.dummy_mode or not self.is_connected:
            print("🔄 虚拟模式：跳过校准图形设置")
            return True
        
        try:
            if EyeLinkCoreGraphicsPsychoPy is None:
                print("✗ EyeLinkCoreGraphicsPsychoPy不可用")
                return False
            
            # 创建图形环境
            self.genv = EyeLinkCoreGraphicsPsychoPy(self.tracker, self.win)
            
            # 设置校准颜色
            foreground_color = (-1, -1, -1)  # 黑色
            background_color = self.win.color
            self.genv.setCalibrationColors(foreground_color, background_color)
            
            # 设置校准目标
            self.genv.setTargetType('circle')
            self.genv.setTargetSize(24)
            
            # 设置声音（关闭）
            self.genv.setCalibrationSounds('', '', '')
            
            # 注册图形环境
            pylink.openGraphicsEx(self.genv)
            
            print("✓ 校准图形环境设置完成")
            return True
            
        except Exception as e:
            print(f"✗ 校准图形环境设置失败: {e}")
            return False
    
    def run_calibration(self):
        """执行校准"""
        print("\n🎯 开始校准...")
        
        if self.dummy_mode:
            # 虚拟模式：显示模拟校准
            self.show_message("虚拟模式校准\n\n按任意键继续", wait_time=2)
            return True
        
        try:
            # 执行EyeLink校准
            self.tracker.doTrackerSetup()
            print("✓ 校准完成")
            return True
            
        except Exception as e:
            print(f"✗ 校准失败: {e}")
            return False
    
    def generate_validation_points(self, num_points=8):
        """生成验证点位置"""
        # 四个角落 + 四个边缘中点
        w, h = self.screen_width, self.screen_height
        margin = 100  # 边距
        
        points = [
            # 四个角落
            (-w//2 + margin, -h//2 + margin),    # 左上
            (w//2 - margin, -h//2 + margin),     # 右上
            (-w//2 + margin, h//2 - margin),     # 左下
            (w//2 - margin, h//2 - margin),      # 右下
            # 四个边缘中点
            (0, -h//2 + margin),                 # 上中
            (0, h//2 - margin),                  # 下中
            (-w//2 + margin, 0),                 # 左中
            (w//2 - margin, 0),                  # 右中
        ]
        
        # 随机打乱顺序
        random.shuffle(points)
        self.validation_points = points[:num_points]
        print(f"✓ 生成{len(self.validation_points)}个验证点")
    
    def get_gaze_position(self):
        """获取当前注视位置"""
        if self.dummy_mode or not self.is_connected:
            # 虚拟模式：使用鼠标位置
            mouse_pos = event.Mouse(win=self.win).getPos()
            return mouse_pos
        
        try:
            # 获取最新的眼动数据
            dt = self.tracker.getNewestSample()
            if dt is not None:
                if dt.isRightSample():
                    gaze_pos = dt.getRightEye().getGaze()
                elif dt.isLeftSample():
                    gaze_pos = dt.getLeftEye().getGaze()
                else:
                    return None
                
                # 转换坐标系（EyeLink坐标 -> PsychoPy坐标）
                x = gaze_pos[0] - self.screen_width // 2
                y = self.screen_height // 2 - gaze_pos[1]
                return (x, y)
            
        except Exception as e:
            print(f"获取注视位置失败: {e}")
        
        return None
    
    def show_message(self, text, wait_time=None):
        """显示消息"""
        msg = visual.TextStim(
            self.win, 
            text=text,
            color='black',
            height=30,
            wrapWidth=self.screen_width * 0.8
        )
        
        self.win.flip()
        msg.draw()
        self.win.flip()
        
        if wait_time:
            core.wait(wait_time)
        else:
            event.waitKeys()
    
    def run_validation(self):
        """运行验证程序"""
        print("\n🔍 开始验证...")
        
        # 生成验证点
        self.generate_validation_points()
        
        # 开始记录（如果连接了EyeLink）
        if self.is_connected and not self.dummy_mode:
            self.tracker.startRecording(1, 1, 1, 1)
        
        # 显示说明
        instruction = """校准验证程序
        
屏幕上会依次出现绿色圆点
请注视每个圆点，观察红色光标是否准确

红色光标 = 您当前的注视位置
绿色圆点 = 目标位置

按空格键开始下一个点
按ESC键退出

按任意键开始..."""
        
        self.show_message(instruction)
        
        # 逐个显示验证点
        for i, point_pos in enumerate(self.validation_points):
            self.show_validation_point(point_pos, i + 1)
        
        # 停止记录
        if self.is_connected and not self.dummy_mode:
            self.tracker.stopRecording()
        
        # 显示完成消息
        self.show_message("验证完成！\n\n按任意键退出", wait_time=2)
    
    def show_validation_point(self, target_pos, point_num):
        """显示单个验证点"""
        print(f"显示验证点 {point_num}/{len(self.validation_points)}: {target_pos}")
        
        # 创建目标点（绿色）
        target = visual.Circle(
            self.win,
            radius=15,
            pos=target_pos,
            fillColor='green',
            lineColor='darkgreen'
        )
        
        # 创建注视光标（红色）
        gaze_cursor = visual.Circle(
            self.win,
            radius=8,
            fillColor='red',
            lineColor='darkred'
        )
        
        # 创建信息文本
        info_text = visual.TextStim(
            self.win,
            text=f"验证点 {point_num}/{len(self.validation_points)}\n注视绿色圆点\n空格键=下一个，ESC=退出",
            pos=(0, -self.screen_height//2 + 50),
            color='black',
            height=20
        )
        
        # 显示循环
        clock = core.Clock()
        while True:
            # 获取当前注视位置
            gaze_pos = self.get_gaze_position()
            
            # 清屏
            self.win.flip()
            
            # 绘制目标点
            target.draw()
            
            # 绘制注视光标
            if gaze_pos:
                gaze_cursor.pos = gaze_pos
                gaze_cursor.draw()
            
            # 绘制信息
            info_text.draw()
            
            # 更新显示
            self.win.flip()
            
            # 检查按键
            keys = event.getKeys()
            if 'escape' in keys:
                return False
            elif 'space' in keys:
                return True
            
            # 限制帧率
            core.wait(0.016)  # ~60 FPS
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 清理资源...")
        
        # 关闭EyeLink连接
        if self.is_connected and not self.dummy_mode:
            try:
                self.tracker.closeDataFile()
                self.tracker.close()
                print("✓ EyeLink连接已关闭")
            except:
                pass
        
        # 关闭窗口
        if self.win:
            self.win.close()
            print("✓ 显示窗口已关闭")
        
        core.quit()
    
    def run(self):
        """运行完整的校准验证流程"""
        try:
            # 1. 初始化显示
            if not self.initialize_display():
                return False
            
            # 2. 连接EyeLink
            if not self.connect_eyelink():
                return False
            
            # 3. 设置校准图形
            if not self.setup_calibration_graphics():
                return False
            
            # 4. 执行校准
            if not self.run_calibration():
                return False
            
            # 5. 运行验证
            self.run_validation()
            
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断程序")
            return False
        except Exception as e:
            print(f"\n程序出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    print("🎯 简化EyeLink校准验证程序")
    print("=" * 50)
    
    # 询问运行模式
    mode = input("选择运行模式 (1=真实EyeLink, 2=虚拟模式): ").strip()
    dummy_mode = (mode == '2')
    
    if dummy_mode:
        print("🔄 使用虚拟模式（鼠标模拟眼动）")
    else:
        print("👁️ 使用真实EyeLink模式")
    
    # 创建并运行校准验证系统
    validator = SimpleCalibrationValidator(dummy_mode=dummy_mode, fullscreen=True)
    success = validator.run()
    
    if success:
        print("✅ 程序执行完成")
    else:
        print("❌ 程序执行失败")

if __name__ == "__main__":
    main()
